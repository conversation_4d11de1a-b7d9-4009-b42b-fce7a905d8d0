using Microsoft.Extensions.Localization;
using BookStoreHtmxAlpineJs.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Ui.Branding;

namespace BookStoreHtmxAlpineJs;

[Dependency(ReplaceServices = true)]
public class BookStoreHtmxAlpineJsBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<BookStoreHtmxAlpineJsResource> _localizer;

    public BookStoreHtmxAlpineJsBrandingProvider(IStringLocalizer<BookStoreHtmxAlpineJsResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}