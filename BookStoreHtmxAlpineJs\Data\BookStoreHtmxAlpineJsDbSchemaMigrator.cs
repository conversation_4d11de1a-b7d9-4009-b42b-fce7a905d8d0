﻿using Volo.Abp.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace BookStoreHtmxAlpineJs.Data;

public class BookStoreHtmxAlpineJsDbSchemaMigrator : ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public BookStoreHtmxAlpineJsDbSchemaMigrator(
        IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        
        /* We intentionally resolving the BookStoreHtmxAlpineJsDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<BookStoreHtmxAlpineJsDbContext>()
            .Database
            .MigrateAsync();

    }
}
