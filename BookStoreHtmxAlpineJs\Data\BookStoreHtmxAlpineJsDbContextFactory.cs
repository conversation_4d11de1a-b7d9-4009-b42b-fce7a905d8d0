﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace BookStoreHtmxAlpineJs.Data;

public class BookStoreHtmxAlpineJsDbContextFactory : IDesignTimeDbContextFactory<BookStoreHtmxAlpineJsDbContext>
{
    public BookStoreHtmxAlpineJsDbContext CreateDbContext(string[] args)
    {
        BookStoreHtmxAlpineJsEfCoreEntityExtensionMappings.Configure();
        var configuration = BuildConfiguration();

        var builder = new DbContextOptionsBuilder<BookStoreHtmxAlpineJsDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));

        return new BookStoreHtmxAlpineJsDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}