﻿using System;
using System.Threading.Tasks;
using BookStoreHtmxAlpineJs.Services.Books;
using BookStoreHtmxAlpineJs.Services.Dtos.Books;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace BookStoreHtmxAlpineJs.Pages.Books;

public class EditModalModel : AbpPageModel
{
    [HiddenInput]
    [BindProperty(SupportsGet = true)]
    public Guid Id { get; set; }

    [BindProperty]
    public CreateUpdateBookDto Book { get; set; }

    private readonly IBookAppService _bookAppService;

    public EditModalModel(IBookAppService bookAppService)
    {
        _bookAppService = bookAppService;
    }

    public async Task OnGetAsync()
    {
        var bookDto = await _bookAppService.GetAsync(Id);
        Book = ObjectMapper.Map<BookDto, CreateUpdateBookDto>(bookDto);
    }

    public async Task<IActionResult> OnPostAsync()
    {
        await _bookAppService.UpdateAsync(Id, Book);

        // Return HTMX response to close modal and trigger refresh
        Response.Headers.Add("HX-Trigger", "book-updated");
        return new JsonResult(new { success = true, message = L["SuccessfullyUpdated"].Value });
    }
}