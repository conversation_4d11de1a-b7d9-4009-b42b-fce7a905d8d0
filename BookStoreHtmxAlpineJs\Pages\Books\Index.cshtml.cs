using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BookStoreHtmxAlpineJs.Services.Books;
using BookStoreHtmxAlpineJs.Services.Dtos.Books;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace BookStoreHtmxAlpineJs.Pages.Books;

public class IndexModel : AbpPageModel
{
    private readonly IBookAppService _bookAppService;

    public IndexModel(IBookAppService bookAppService)
    {
        _bookAppService = bookAppService;
    }

    public void OnGet()
    {

    }

    // HTMX endpoint for table data
    public async Task<IActionResult> OnGetTableAsync(int page = 1, int size = 10, string sort = "Name")
    {
        var input = new PagedAndSortedResultRequestDto
        {
            SkipCount = (page - 1) * size,
            MaxResultCount = size,
            Sorting = sort
        };

        var result = await _bookAppService.GetListAsync(input);

        var viewModel = new BooksTableViewModel
        {
            Books = result.Items,
            TotalCount = result.TotalCount,
            CurrentPage = page,
            PageSize = size,
            Sort = sort
        };

        return Partial("_BooksTable", viewModel);
    }

    // HTMX endpoint for deleting a book
    public async Task<IActionResult> OnDeleteAsync(Guid id)
    {
        await _bookAppService.DeleteAsync(id);

        // Return success response for HTMX
        Response.Headers.Add("HX-Trigger", "book-deleted");
        return new JsonResult(new { success = true, message = L["SuccessfullyDeleted"].Value });
    }
}

// ViewModel for the books table partial
public class BooksTableViewModel
{
    public IReadOnlyList<BookDto> Books { get; set; } = new List<BookDto>();
    public long TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public string Sort { get; set; } = string.Empty;

    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => CurrentPage > 1;
    public bool HasNextPage => CurrentPage < TotalPages;
}