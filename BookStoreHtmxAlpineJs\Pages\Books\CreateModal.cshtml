﻿@page
@using BookStoreHtmxAlpineJs.Localization
@using BookStoreHtmxAlpineJs.Pages.Books
@using Microsoft.Extensions.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model CreateModalModel
@inject IStringLocalizer<BookStoreHtmxAlpineJsResource> L
@{
    Layout = null;
}

<div x-data="abpAlpine.createData({
    showModal: true,

    closeModal() {
        this.showModal = false;
        document.getElementById('modal-container').innerHTML = '';
    },

    submitForm() {
        const form = document.getElementById('create-book-form');
        const formData = new FormData(form);

        htmx.ajax('POST', '/Books/CreateModal', {
            values: formData,
            target: '#modal-container',
            swap: 'innerHTML'
        }).then(() => {
            this.closeModal();
        });
    }
})" x-show="showModal" x-transition>

    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form id="create-book-form"
                      hx-post="/Books/CreateModal"
                      hx-target="#modal-container"
                      hx-swap="innerHTML"
                      x-on:submit.prevent="submitForm()">

                    <div class="modal-header">
                        <h5 class="modal-title">@L["NewBook"]</h5>
                        <button type="button" class="btn-close" x-on:click="closeModal()"></button>
                    </div>

                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="Book_Name" class="form-label">@L["Name"] *</label>
                            <input type="text" class="form-control" id="Book_Name" name="Book.Name" required maxlength="128" />
                        </div>

                        <div class="mb-3">
                            <label for="Book_Type" class="form-label">@L["Type"] *</label>
                            <select class="form-select" id="Book_Type" name="Book.Type" required>
                                <option value="0">@L["Enum:BookType.0"]</option>
                                <option value="1">@L["Enum:BookType.1"]</option>
                                <option value="2">@L["Enum:BookType.2"]</option>
                                <option value="3">@L["Enum:BookType.3"]</option>
                                <option value="4">@L["Enum:BookType.4"]</option>
                                <option value="5">@L["Enum:BookType.5"]</option>
                                <option value="6">@L["Enum:BookType.6"]</option>
                                <option value="7">@L["Enum:BookType.7"]</option>
                                <option value="8">@L["Enum:BookType.8"]</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="Book_PublishDate" class="form-label">@L["PublishDate"] *</label>
                            <input type="date" class="form-control" id="Book_PublishDate" name="Book.PublishDate" required />
                        </div>

                        <div class="mb-3">
                            <label for="Book_Price" class="form-label">@L["Price"] *</label>
                            <input type="number" class="form-control" id="Book_Price" name="Book.Price" step="0.01" min="0" required />
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" x-on:click="closeModal()">@L["Close"]</button>
                        <button type="submit" class="btn btn-primary">@L["Save"]</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal backdrop -->
    <div class="modal-backdrop fade show" x-on:click="closeModal()"></div>
</div>
