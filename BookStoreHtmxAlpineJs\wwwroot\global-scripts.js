/* Your Global Scripts */

// HTMX and Alpine.js Integration with ABP Framework
(function() {
    'use strict';

    // ABP-HTMX Integration Helpers
    window.abpHtmx = {
        // Configure HTMX with ABP's CSRF token
        configureAntiForgery: function() {
            if (typeof htmx !== 'undefined' && abp.security && abp.security.antiForgery) {
                htmx.config.requestClass = 'htmx-request';

                // Add CSRF token to all HTMX requests
                document.body.addEventListener('htmx:configRequest', function(evt) {
                    if (abp.security.antiForgery.token) {
                        evt.detail.headers[abp.security.antiForgery.tokenHeaderName] = abp.security.antiForgery.token;
                    }
                });
            }
        },

        // Show loading indicator for HTMX requests
        configureLoadingIndicator: function() {
            if (typeof htmx !== 'undefined') {
                document.body.addEventListener('htmx:beforeRequest', function(evt) {
                    abp.ui.setBusy();
                });

                document.body.addEventListener('htmx:afterRequest', function(evt) {
                    abp.ui.clearBusy();
                });
            }
        },

        // Handle ABP notifications for HTMX responses
        configureNotifications: function() {
            if (typeof htmx !== 'undefined') {
                document.body.addEventListener('htmx:responseError', function(evt) {
                    if (evt.detail.xhr.status === 403) {
                        abp.notify.error(abp.localization.getResource('AbpUi')('403Message'));
                    } else if (evt.detail.xhr.status === 404) {
                        abp.notify.error(abp.localization.getResource('AbpUi')('404Message'));
                    } else if (evt.detail.xhr.status >= 500) {
                        abp.notify.error(abp.localization.getResource('AbpUi')('InternalServerErrorMessage'));
                    }
                });
            }
        },

        // Initialize all HTMX configurations
        initialize: function() {
            this.configureAntiForgery();
            this.configureLoadingIndicator();
            this.configureNotifications();
        }
    };

    // ABP-Alpine.js Integration Helpers
    window.abpAlpine = {
        // Create Alpine.js data with ABP localization support
        createData: function(initialData) {
            return Object.assign({
                // Helper method to get localized text
                L: function(key, ...args) {
                    if (typeof abp !== 'undefined' && abp.localization) {
                        const resource = abp.localization.getResource('BookStoreHtmxAlpineJs');
                        return resource(key, ...args);
                    }
                    return key;
                },

                // Helper method to check permissions
                hasPermission: function(permission) {
                    return typeof abp !== 'undefined' && abp.auth && abp.auth.isGranted(permission);
                },

                // Helper method to show notifications
                notify: function(message, type = 'info') {
                    if (typeof abp !== 'undefined' && abp.notify) {
                        abp.notify[type](message);
                    }
                },

                // Helper method to confirm actions
                confirm: function(message, callback) {
                    if (typeof abp !== 'undefined' && abp.message) {
                        abp.message.confirm(message).then(callback);
                    } else {
                        if (window.confirm(message)) {
                            callback(true);
                        }
                    }
                }
            }, initialData || {});
        }
    };

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize HTMX integration
        if (typeof htmx !== 'undefined') {
            abpHtmx.initialize();
        }

        // Initialize Alpine.js
        if (typeof Alpine !== 'undefined') {
            Alpine.start();
        }
    });

})();
