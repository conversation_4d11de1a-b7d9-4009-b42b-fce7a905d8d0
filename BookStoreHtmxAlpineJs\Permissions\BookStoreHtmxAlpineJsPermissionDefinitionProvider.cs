using BookStoreHtmxAlpineJs.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace BookStoreHtmxAlpineJs.Permissions;

public class BookStoreHtmxAlpineJsPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(BookStoreHtmxAlpineJsPermissions.GroupName);


        myGroup.AddPermission(BookStoreHtmxAlpineJsPermissions.Dashboard.Host, L("Permission:Dashboard"), MultiTenancySides.Host);

        var booksPermission = myGroup.AddPermission(BookStoreHtmxAlpineJsPermissions.Books.Default, L("Permission:Books"));
        booksPermission.AddChild(BookStoreHtmxAlpineJsPermissions.Books.Create, L("Permission:Books.Create"));
        booksPermission.AddChild(BookStoreHtmxAlpineJsPermissions.Books.Edit, L("Permission:Books.Edit"));
        booksPermission.AddChild(BookStoreHtmxAlpineJsPermissions.Books.Delete, L("Permission:Books.Delete"));
        
        //Define your own permissions here. Example:
        //myGroup.AddPermission(BookStoreHtmxAlpineJsPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<BookStoreHtmxAlpineJsResource>(name);
    }
}
