@using BookStoreHtmxAlpineJs.Localization
@using BookStoreHtmxAlpineJs.Permissions
@using BookStoreHtmxAlpineJs.Pages.Books
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@model BooksTableViewModel
@inject IStringLocalizer<BookStoreHtmxAlpineJsResource> L
@inject IAuthorizationService AuthorizationService

<div id="books-table-container">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>
                    <a href="#" 
                       hx-get="/Books?handler=Table&sort=@(Model.Sort == "Name" ? "Name desc" : "Name")&page=@Model.CurrentPage&size=@Model.PageSize"
                       hx-target="#books-table-container"
                       hx-swap="outerHTML">
                        @L["Name"]
                        @if (Model.Sort == "Name")
                        {
                            <i class="fa fa-sort-up"></i>
                        }
                        else if (Model.Sort == "Name desc")
                        {
                            <i class="fa fa-sort-down"></i>
                        }
                        else
                        {
                            <i class="fa fa-sort"></i>
                        }
                    </a>
                </th>
                <th>
                    <a href="#" 
                       hx-get="/Books?handler=Table&sort=@(Model.Sort == "Type" ? "Type desc" : "Type")&page=@Model.CurrentPage&size=@Model.PageSize"
                       hx-target="#books-table-container"
                       hx-swap="outerHTML">
                        @L["Type"]
                        @if (Model.Sort == "Type")
                        {
                            <i class="fa fa-sort-up"></i>
                        }
                        else if (Model.Sort == "Type desc")
                        {
                            <i class="fa fa-sort-down"></i>
                        }
                        else
                        {
                            <i class="fa fa-sort"></i>
                        }
                    </a>
                </th>
                <th>
                    <a href="#" 
                       hx-get="/Books?handler=Table&sort=@(Model.Sort == "PublishDate" ? "PublishDate desc" : "PublishDate")&page=@Model.CurrentPage&size=@Model.PageSize"
                       hx-target="#books-table-container"
                       hx-swap="outerHTML">
                        @L["PublishDate"]
                        @if (Model.Sort == "PublishDate")
                        {
                            <i class="fa fa-sort-up"></i>
                        }
                        else if (Model.Sort == "PublishDate desc")
                        {
                            <i class="fa fa-sort-down"></i>
                        }
                        else
                        {
                            <i class="fa fa-sort"></i>
                        }
                    </a>
                </th>
                <th>
                    <a href="#" 
                       hx-get="/Books?handler=Table&sort=@(Model.Sort == "Price" ? "Price desc" : "Price")&page=@Model.CurrentPage&size=@Model.PageSize"
                       hx-target="#books-table-container"
                       hx-swap="outerHTML">
                        @L["Price"]
                        @if (Model.Sort == "Price")
                        {
                            <i class="fa fa-sort-up"></i>
                        }
                        else if (Model.Sort == "Price desc")
                        {
                            <i class="fa fa-sort-down"></i>
                        }
                        else
                        {
                            <i class="fa fa-sort"></i>
                        }
                    </a>
                </th>
                <th>
                    <a href="#" 
                       hx-get="/Books?handler=Table&sort=@(Model.Sort == "CreationTime" ? "CreationTime desc" : "CreationTime")&page=@Model.CurrentPage&size=@Model.PageSize"
                       hx-target="#books-table-container"
                       hx-swap="outerHTML">
                        @L["CreationTime"]
                        @if (Model.Sort == "CreationTime")
                        {
                            <i class="fa fa-sort-up"></i>
                        }
                        else if (Model.Sort == "CreationTime desc")
                        {
                            <i class="fa fa-sort-down"></i>
                        }
                        else
                        {
                            <i class="fa fa-sort"></i>
                        }
                    </a>
                </th>
                <th>@L["Actions"]</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var book in Model.Books)
            {
                <tr id="<EMAIL>">
                    <td>@book.Name</td>
                    <td>@L[$"Enum:BookType.{(int)book.Type}"]</td>
                    <td>@book.PublishDate.ToString("yyyy-MM-dd")</td>
                    <td>@book.Price.ToString("C")</td>
                    <td>@book.CreationTime.ToString("yyyy-MM-dd HH:mm")</td>
                    <td>
                        <div class="btn-group" role="group">
                            @if (await AuthorizationService.IsGrantedAsync(BookStoreHtmxAlpineJsPermissions.Books.Edit))
                            {
                                <button type="button" 
                                        class="btn btn-primary btn-sm"
                                        hx-get="/Books/EditModal?id=@book.Id"
                                        hx-target="#modal-container"
                                        hx-swap="innerHTML">
                                    <i class="fa fa-edit"></i> @L["Edit"]
                                </button>
                            }
                            @if (await AuthorizationService.IsGrantedAsync(BookStoreHtmxAlpineJsPermissions.Books.Delete))
                            {
                                <button type="button" 
                                        class="btn btn-danger btn-sm"
                                        x-data="{ 
                                            deleteBook() { 
                                                this.confirm('@L["BookDeletionConfirmationMessage", book.Name]', (confirmed) => {
                                                    if (confirmed) {
                                                        htmx.ajax('DELETE', '/Books?handler=Delete&id=@book.Id', {
                                                            target: '#<EMAIL>',
                                                            swap: 'delete'
                                                        });
                                                    }
                                                });
                                            }
                                        }"
                                        x-on:click="deleteBook()">
                                    <i class="fa fa-trash"></i> @L["Delete"]
                                </button>
                            }
                        </div>
                    </td>
                </tr>
            }
        </tbody>
    </table>

    <!-- Pagination -->
    @if (Model.TotalPages > 1)
    {
        <nav aria-label="Books pagination">
            <ul class="pagination justify-content-center">
                <li class="page-item @(Model.HasPreviousPage ? "" : "disabled")">
                    <a class="page-link" 
                       href="#"
                       hx-get="/Books?handler=Table&page=@(Model.CurrentPage - 1)&size=@Model.PageSize&sort=@Model.Sort"
                       hx-target="#books-table-container"
                       hx-swap="outerHTML">
                        @L["Previous"]
                    </a>
                </li>

                @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                {
                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                        <a class="page-link" 
                           href="#"
                           hx-get="/Books?handler=Table&page=@i&size=@Model.PageSize&sort=@Model.Sort"
                           hx-target="#books-table-container"
                           hx-swap="outerHTML">
                            @i
                        </a>
                    </li>
                }

                <li class="page-item @(Model.HasNextPage ? "" : "disabled")">
                    <a class="page-link" 
                       href="#"
                       hx-get="/Books?handler=Table&page=@(Model.CurrentPage + 1)&size=@Model.PageSize&sort=@Model.Sort"
                       hx-target="#books-table-container"
                       hx-swap="outerHTML">
                        @L["Next"]
                    </a>
                </li>
            </ul>
        </nav>
    }

    <!-- Results info -->
    <div class="d-flex justify-content-between align-items-center mt-3">
        <small class="text-muted">
            @L["ShowingResults", (Model.CurrentPage - 1) * Model.PageSize + 1, Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount), Model.TotalCount]
        </small>
        <div>
            <label for="page-size-select" class="form-label">@L["ItemsPerPage"]:</label>
            <select id="page-size-select" 
                    class="form-select form-select-sm d-inline-block w-auto"
                    hx-get="/Books?handler=Table&page=1&sort=@Model.Sort"
                    hx-target="#books-table-container"
                    hx-swap="outerHTML"
                    hx-include="this"
                    name="size">
                <option value="10" selected="@(Model.PageSize == 10)">10</option>
                <option value="25" selected="@(Model.PageSize == 25)">25</option>
                <option value="50" selected="@(Model.PageSize == 50)">50</option>
                <option value="100" selected="@(Model.PageSize == 100)">100</option>
            </select>
        </div>
    </div>
</div>
