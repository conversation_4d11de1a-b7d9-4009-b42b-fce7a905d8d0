﻿@page
@using BookStoreHtmxAlpineJs.Localization
@using BookStoreHtmxAlpineJs.Permissions
@using BookStoreHtmxAlpineJs.Pages.Books
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@model IndexModel
@inject IStringLocalizer<BookStoreHtmxAlpineJsResource> L
@inject IAuthorizationService AuthorizationService

<div x-data="abpAlpine.createData({
    init() {
        // Listen for book-deleted event to refresh the table
        document.body.addEventListener('book-deleted', () => {
            htmx.ajax('GET', '/Books?handler=Table&page=1&size=10&sort=Name', {
                target: '#books-table-container',
                swap: 'outerHTML'
            });
            this.notify(this.L('SuccessfullyDeleted'), 'success');
        });

        // Listen for book-created event to refresh the table
        document.body.addEventListener('book-created', () => {
            htmx.ajax('GET', '/Books?handler=Table&page=1&size=10&sort=Name', {
                target: '#books-table-container',
                swap: 'outerHTML'
            });
            this.notify(this.L('SuccessfullyCreated'), 'success');
        });

        // Listen for book-updated event to refresh the table
        document.body.addEventListener('book-updated', () => {
            htmx.ajax('GET', '/Books?handler=Table&page=1&size=10&sort=Name', {
                target: '#books-table-container',
                swap: 'outerHTML'
            });
            this.notify(this.L('SuccessfullyUpdated'), 'success');
        });
    },

    openCreateModal() {
        htmx.ajax('GET', '/Books/CreateModal', {
            target: '#modal-container',
            swap: 'innerHTML'
        });
    }
})">
    <abp-card>
        <abp-card-header>
            <abp-row>
                <abp-column size-md="_6">
                    <abp-card-title>@L["Books"]</abp-card-title>
                </abp-column>
                <abp-column size-md="_6" class="text-end">
                    @if (await AuthorizationService.IsGrantedAsync(BookStoreHtmxAlpineJsPermissions.Books.Create))
                    {
                        <button type="button"
                                class="btn btn-primary"
                                x-on:click="openCreateModal()">
                            <i class="fa fa-plus"></i> @L["NewBook"]
                        </button>
                    }
                </abp-column>
            </abp-row>
        </abp-card-header>
        <abp-card-body>
            <!-- Initial load of the books table -->
            <div hx-get="/Books?handler=Table&page=1&size=10&sort=Name"
                 hx-trigger="load"
                 hx-target="this"
                 hx-swap="innerHTML">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">@L["Loading"]...</span>
                    </div>
                </div>
            </div>
        </abp-card-body>
    </abp-card>

    <!-- Modal container for create/edit modals -->
    <div id="modal-container"></div>
</div>